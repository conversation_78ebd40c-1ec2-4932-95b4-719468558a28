# Find variable names by label pattern across 0810 years
suppressPackageStartupMessages({
  library(haven)
  library(dplyr)
})

data_dir <- '/home/<USER>/vaping2_working_data/core_data_0810'
years <- 2000:2006

find_first_match <- function(file, pattern){
  d <- read_dta(file)
  labs <- sapply(d, function(x) as.character(attr(x,'label')))
  nms <- names(d)
  idx <- which(grepl(pattern, labs, ignore.case=TRUE))
  if(length(idx)==0) return(NA_character_)
  nms[idx[1]]
}

patterns <- list(
  race = "R'S RACE|R S RACE|^RACE$",
  female = "R'S SEX|^SEX$",
  region = 'SCHOOL REGION|REGION',
  cig = 'CIGS SMKD/30DAY',
  cannabis = 'XMJ\\+HS/LAST30',
  alcohol = '#X DRNK/LAST30|DRNK/LAST30'
)

for (key in names(patterns)){
  cat('\n--', key, '--\n')
  pat <- patterns[[key]]
  for (yr in years){
    f <- file.path(data_dir, sprintf('%d-0001-0810-Data.dta', yr))
    v <- find_first_match(f, pat)
    cat(yr, '=>', ifelse(is.na(v), 'NA', v), '\n')
  }
}

