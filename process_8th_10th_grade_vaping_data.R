# Process 8th and 10th Grade Vaping Data - Read STATA datasets, apply recoding, and create appended datasets
# Author: Generated script for 8th and 10th grade vaping data processing
# Date: 2025-08-14

# Load required libraries
library(haven)
library(dplyr)
library(readr)

# Set working directory and paths
data_path <- "/home/<USER>/vaping2_working_data/core_data_0810"
syntax_path <- "/home/<USER>/vaping2_working_data/core_data_0810_supplemental_syntax"
output_path <- "/home/<USER>/vaping2"

# Variables to keep in final datasets
keep_vars <- c("CASEID", "cig", "region", "age18", "female", "pared", "skip",
               "gpa", "goout", "work", "ccannabis", "cdrink", "wrace", "collexp",
               "dates", "V5", "cvape")


# Function to create mapped variables for 2000-2006 datasets (0810 series)
create_mapped_variables <- function(data) {
  # Convert labelled data to numeric for processing
  data_unlabelled <- lapply(data, function(x) {
    if (inherits(x, "haven_labelled")) {
      as.numeric(x)
    } else {
      x
    }
  })
  data_unlabelled <- as.data.frame(data_unlabelled)

  # Helper to pick first present variable from candidates
  pick <- function(cands) {
    present <- cands[cands %in% names(data_unlabelled)]
    if (length(present) == 0) return(NULL)
    data_unlabelled[[present[1]]]
  }

  mapped_data <- data.frame(
    CASEID = data_unlabelled$CASEID,
    V5 = data_unlabelled$V5
  )

  # region
  mapped_data$region <- pick(c("V507"))

  # female (1=male, 2=female) -> binary female
  female_src <- pick(c("V1233", "V1232", "V1246"))
  mapped_data$female <- if (!is.null(female_src)) ifelse(female_src == 1, 0, ifelse(female_src == 2, 1, NA)) else NA

  # race: recode white=1, black/other=0 (based on labels where 0=WHITE, 1=BLACK)
  race_src <- pick(c("V1234", "V1233", "V1247"))
  mapped_data$wrace <- if (!is.null(race_src)) ifelse(race_src == 0, 1, ifelse(race_src == 1, 0, NA)) else NA

  # parental education (max of father/mother; treat 7=DK as NA if present in any year)
  dad <- pick(c("V1246", "V1245", "V1259"))
  mom <- pick(c("V1247", "V1246", "V1260"))
  if (!is.null(dad) || !is.null(mom)) {
    dad2 <- if (!is.null(dad)) ifelse(dad == 7, NA, dad) else NA
    mom2 <- if (!is.null(mom)) ifelse(mom == 7, NA, mom) else NA
    mapped_data$pared <- pmax(dad2, mom2, na.rm = TRUE)
    mapped_data$pared[is.na(dad2) & is.na(mom2)] <- NA
  } else {
    mapped_data$pared <- NA
  }

  # skip class
  mapped_data$skip <- pick(c("V1265", "V1264", "V1278"))

  # GPA
  mapped_data$gpa <- pick(c("V1252", "V1251", "V1265"))

  # dating frequency
  mapped_data$dates <- pick(c("V1271", "V1270", "V1284"))

  # college expectations
  mapped_data$collexp <- pick(c("V1258", "V1257", "V1271"))

  # substance use 30-day: recode 1=none -> 0; 2-7 -> 1
  cig_src <- pick(c("V1178", "V1177", "V1191"))
  mapped_data$cig <- if (!is.null(cig_src)) ifelse(cig_src == 1, 0, ifelse(cig_src %in% 2:7, 1, NA)) else NA

  can_src <- pick(c("V1191", "V1190", "V1204"))
  mapped_data$ccannabis <- if (!is.null(can_src)) ifelse(can_src == 1, 0, ifelse(can_src %in% 2:7, 1, NA)) else NA

  alc_src <- pick(c("V1184", "V1183", "V1197"))
  mapped_data$cdrink <- if (!is.null(alc_src)) ifelse(alc_src == 1, 0, ifelse(alc_src %in% 2:7, 1, NA)) else NA

  # Variables likely absent in 0810 extract (set to NA if not found)
  mapped_data$goout <- NA  # Targeted search found no '2+ evenings out per week' measure
  mapped_data$work <- NA   # No 'HRS/W WRK SCHYR' in 0810; only preferred hours present
  mapped_data$age18 <- NA  # No 'AGE 18+' dichotomy present in 0810 labels

  return(mapped_data)
}

# Function to apply missing value recodes based on supplemental syntax
apply_missing_recodes <- function(data, syntax_file) {
  if (!file.exists(syntax_file)) {
    cat("Warning: Syntax file not found:", syntax_file, "\n")
    return(data)
  }
  
  # Read the syntax file
  syntax_lines <- readLines(syntax_file)
  
  # Extract replace commands
  replace_lines <- syntax_lines[grepl("^replace", syntax_lines)]
  
  # Apply each replace command
  for (line in replace_lines) {
    # Parse the replace command: replace VAR = . if (VAR == VALUE)
    if (grepl("replace .+ = \\. if \\(.+ == .+\\)", line)) {
      # Extract variable name and value
      var_match <- regmatches(line, regexpr("(?<=replace )\\w+", line, perl = TRUE))
      val_match <- regmatches(line, regexpr("(?<== )-?\\d+", line, perl = TRUE))
      
      if (length(var_match) > 0 && length(val_match) > 0) {
        var_name <- var_match[1]
        missing_val <- as.numeric(val_match[1])
        
        # Apply the recode if variable exists in data
        if (var_name %in% names(data)) {
          data[[var_name]][data[[var_name]] == missing_val] <- NA
          cat("Applied missing recode for", var_name, ":", missing_val, "-> NA\n")
        }
      }
    }
  }
  
  return(data)
}

# Function to process a single dataset
process_dataset <- function(year_code) {
  # For 0810 datasets, filenames include full 4-digit year and '-0001-0810-'
  data_file <- file.path(data_path, paste0(year_code, "-0001-0810-Data.dta"))
  syntax_file <- file.path(syntax_path, paste0(year_code, "-0001-0810-Supplemental_syntax.do"))
  
  cat("Processing dataset for year code:", year_code, "\n")
  
  # Read the dataset
  if (!file.exists(data_file)) {
    cat("Warning: Data file not found:", data_file, "\n")
    return(NULL)
  }
  
  data <- read_dta(data_file)
  cat("Loaded dataset with", nrow(data), "rows and", ncol(data), "columns\n")
  
  # Apply missing value recodes
  data <- apply_missing_recodes(data, syntax_file)

  # Create mapped variables for 2000-2006 datasets
  mapped_data <- create_mapped_variables(data)

  # Add year variable (0810 uses 4-digit year_code)
  mapped_data$year <- as.numeric(year_code)

  return(mapped_data)
}

# Process datasets for 2000-2006 (0810 data: 2000-2006)
cat("=== Processing 2000-2006 datasets (0810) ===\n")
datasets_2000_2006 <- list()
year_codes_2000_2006 <- c("2000", "2001", "2002", "2003", "2004", "2005", "2006")

for (year_code in year_codes_2000_2006) {
  dataset <- process_dataset(year_code)
  if (!is.null(dataset)) {
    datasets_2000_2006[[year_code]] <- dataset
  }
}

# Append 2000-2006 datasets
if (length(datasets_2000_2006) > 0) {
  cat("Appending 2000-2006 datasets...\n")

  # Find common variables across all datasets
  all_vars <- lapply(datasets_2000_2006, names)
  common_vars <- Reduce(intersect, all_vars)
  cat("Common variables across datasets:", length(common_vars), "\n")

  # Keep only common variables and unlabel data to avoid precision issues
  datasets_2000_2006 <- lapply(datasets_2000_2006, function(df) {
    df_common <- df[common_vars]
    # Convert labelled data to regular data
    df_unlabelled <- lapply(df_common, function(x) {
      if (inherits(x, "haven_labelled")) {
        as.vector(x)
      } else {
        x
      }
    })
    as.data.frame(df_unlabelled)
  })

  # Combine datasets
  combined_2000_2006 <- do.call(rbind, datasets_2000_2006)
  
  # Keep all available mapped variables (they should all be present now)
  all_vars_2000_2006 <- names(combined_2000_2006)
  available_keep_vars <- intersect(keep_vars, all_vars_2000_2006)
  cat("Available variables from keep list:", length(available_keep_vars), "out of", length(keep_vars), "\n")
  cat("Available variables:", paste(available_keep_vars, collapse = ", "), "\n")
  cat("All variables in dataset:", paste(all_vars_2000_2006, collapse = ", "), "\n")

  # Use all available variables (should include most/all mapped variables now)
  final_2000_2006 <- combined_2000_2006

  # Export to Stata format
  output_file_2000_2006 <- file.path(output_path, "vaping_8th_10th_grade_2000_2006.dta")
  write_dta(final_2000_2006, output_file_2000_2006)
  cat("Exported 2000-2006 dataset to:", output_file_2000_2006, "\n")
  cat("Final dataset dimensions:", nrow(final_2000_2006), "rows,", ncol(final_2000_2006), "columns\n")
}

# Process 2017-2023 dataset
cat("\n=== Processing 2017-2023 dataset ===\n")
data_file_2017_2023 <- "/mnt/c/my_folder/vaping2_working_data/2017_2023_jeremy.dta"

if (file.exists(data_file_2017_2023)) {
  cat("Loading 2017-2023 dataset...\n")
  data_2017_2023 <- read_dta(data_file_2017_2023)
  cat("Loaded dataset with", nrow(data_2017_2023), "rows and", ncol(data_2017_2023), "columns\n")
  
  # Map equivalent variables to match 2000-2006 dataset structure
  # RESPONDENT_ID -> CASEID, ARCHIVE_WT -> V5
  if ("RESPONDENT_ID" %in% names(data_2017_2023)) {
    data_2017_2023$CASEID <- data_2017_2023$RESPONDENT_ID
    cat("Mapped RESPONDENT_ID to CASEID\n")
  }

  if ("ARCHIVE_WT" %in% names(data_2017_2023)) {
    data_2017_2023$V5 <- data_2017_2023$ARCHIVE_WT
    cat("Mapped ARCHIVE_WT to V5\n")
  }

  # Keep only specified variables (if they exist)
  available_keep_vars_2017_2023 <- intersect(keep_vars, names(data_2017_2023))
  cat("Available variables from keep list:", length(available_keep_vars_2017_2023), "out of", length(keep_vars), "\n")
  cat("Available variables:", paste(available_keep_vars_2017_2023, collapse = ", "), "\n")

  # Check if year variable exists, if not create one
  if (!"year" %in% names(data_2017_2023)) {
    selected_data <- data_2017_2023[available_keep_vars_2017_2023]
  } else {
    selected_data <- data_2017_2023[c(available_keep_vars_2017_2023, "year")]
  }

  # Derive binary variables per research definitions (retain originals)
  derive_bin <- function(df){
    out <- df
    # GPA A-/A => 1
    if ("gpa" %in% names(out)) out$gpa_bin <- ifelse(out$gpa >= 8, 1, ifelse(is.na(out$gpa), NA, 0))
    # College expectations DEF WILL (4) => 1
    if ("collexp" %in% names(out)) out$collexp_bin <- ifelse(out$collexp == 4, 1, ifelse(is.na(out$collexp), NA, 0))
    # Parental education: either parent college grad or grad school (>=5) => 1
    if ("pared" %in% names(out)) out$pared_bin <- ifelse(out$pared >= 5, 1, ifelse(is.na(out$pared), NA, 0))
    # Skip class in past month: any skipping (2-6) => 1
    if ("skip" %in% names(out)) out$skip_bin <- ifelse(out$skip %in% 2:6, 1, ifelse(out$skip %in% 1, 0, NA))
    # Go out: 2+ evenings/week (>=4) => 1
    if ("goout" %in% names(out)) out$goout_bin <- ifelse(out$goout >= 4, 1, ifelse(is.na(out$goout), NA, 0))
    # Dating: any in past month (>=2) => 1
    if ("dates" %in% names(out)) out$dates_bin <- ifelse(out$dates >= 2, 1, ifelse(is.na(out$dates), NA, 0))
    # Work 21+ hrs/wk (coded 2) => 1
    if ("work" %in% names(out)) out$work21_bin <- ifelse(out$work == 2, 1, ifelse(is.na(out$work), NA, 0))
    # Return
    out
  }

  selected_data <- derive_bin(selected_data)

  # Convert labelled data to regular data
  final_2017_2023 <- lapply(selected_data, function(x) {
    if (inherits(x, "haven_labelled")) as.vector(x) else x
  })
  final_2017_2023 <- as.data.frame(final_2017_2023)
  
  # Export to Stata format
  output_file_2017_2023 <- file.path(output_path, "vaping_8th_10th_grade_2017_2023.dta")
  write_dta(final_2017_2023, output_file_2017_2023)
  cat("Exported 2017-2023 dataset to:", output_file_2017_2023, "\n")
  cat("Final dataset dimensions:", nrow(final_2017_2023), "rows,", ncol(final_2017_2023), "columns\n")
} else {
  cat("Warning: 2017-2023 data file not found:", data_file_2017_2023, "\n")
}

cat("\n=== Processing completed ===\n")
