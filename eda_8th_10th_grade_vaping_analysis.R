# Comprehensive Exploratory Data Analysis for 8th and 10th Grade Vaping Datasets
# Author: Generated EDA script
# Date: 2025-08-14

# Load required libraries
library(haven)
library(dplyr)
library(knitr)
library(ggplot2)
library(corrplot)

# Set options for better output
options(width = 100)

cat("=================================================================\n")
cat("COMPREHENSIVE EDA: 8TH AND 10TH GRADE VAPING DATASETS\n")
cat("=================================================================\n\n")

# Function to calculate missing data summary
missing_summary <- function(data) {
  missing_counts <- sapply(data, function(x) sum(is.na(x)))
  missing_pct <- round(missing_counts / nrow(data) * 100, 2)
  data.frame(
    Variable = names(missing_counts),
    Missing_Count = missing_counts,
    Missing_Percent = missing_pct,
    stringsAsFactors = FALSE
  ) %>% arrange(desc(Missing_Percent))
}

# Function to generate descriptive statistics
desc_stats <- function(data, var_name) {
  if (is.numeric(data[[var_name]])) {
    stats <- data[[var_name]][!is.na(data[[var_name]])]
    if (length(stats) > 0) {
      return(data.frame(
        Variable = var_name,
        N = length(stats),
        Mean = round(mean(stats), 3),
        Median = median(stats),
        Min = min(stats),
        Max = max(stats),
        SD = round(sd(stats), 3),
        stringsAsFactors = FALSE
      ))
    }
  }
  return(NULL)
}

# Function to generate frequency tables
freq_table <- function(data, var_name, max_categories = 10) {
  if (var_name %in% names(data)) {
    freq <- table(data[[var_name]], useNA = "ifany")
    if (length(freq) <= max_categories) {
      return(data.frame(
        Value = names(freq),
        Count = as.numeric(freq),
        Percent = round(as.numeric(freq) / sum(freq) * 100, 2),
        stringsAsFactors = FALSE
      ))
    }
  }
  return(NULL)
}

# Function to calculate substance use prevalence
substance_prevalence <- function(data) {
  substances <- c("cig", "ccannabis", "cdrink")
  results <- data.frame(
    Substance = character(),
    N_Valid = numeric(),
    N_Users = numeric(),
    Prevalence_Percent = numeric(),
    stringsAsFactors = FALSE
  )
  
  for (substance in substances) {
    if (substance %in% names(data)) {
      valid_data <- data[[substance]][!is.na(data[[substance]])]
      if (length(valid_data) > 0) {
        n_users <- sum(valid_data == 1, na.rm = TRUE)
        prevalence <- round(n_users / length(valid_data) * 100, 2)
        results <- rbind(results, data.frame(
          Substance = substance,
          N_Valid = length(valid_data),
          N_Users = n_users,
          Prevalence_Percent = prevalence,
          stringsAsFactors = FALSE
        ))
      }
    }
  }
  return(results)
}

# Load datasets
cat("Loading datasets...\n")
data_2000_2006 <- read_dta("vaping_8th_10th_grade_2000_2006.dta")
data_2017_2023 <- read_dta("vaping_8th_10th_grade_2017_2023.dta")

cat("Datasets loaded successfully!\n\n")

# =================================================================
# DATASET 1: 2000-2006 ANALYSIS
# =================================================================

cat("=================================================================\n")
cat("DATASET 1: 8TH AND 10TH GRADE VAPING DATA 2000-2006\n")
cat("=================================================================\n\n")

# Dataset Structure
cat("--- DATASET STRUCTURE ---\n")
cat("Dimensions:", nrow(data_2000_2006), "rows ×", ncol(data_2000_2006), "columns\n")
cat("Variable names:", paste(names(data_2000_2006), collapse = ", "), "\n")
cat("Variable types:\n")
str(data_2000_2006)

# Years and sample sizes
cat("\n--- YEARS AND SAMPLE SIZES ---\n")
if ("year" %in% names(data_2000_2006)) {
  year_table <- table(data_2000_2006$year)
  print(year_table)
  cat("Year range:", min(data_2000_2006$year, na.rm = TRUE), "-", max(data_2000_2006$year, na.rm = TRUE), "\n")
}

# Missing data analysis
cat("\n--- MISSING DATA ANALYSIS ---\n")
missing_2000_2006 <- missing_summary(data_2000_2006)
print(missing_2000_2006)

# Descriptive statistics for numeric variables
cat("\n--- DESCRIPTIVE STATISTICS (NUMERIC VARIABLES) ---\n")
numeric_vars <- names(data_2000_2006)[sapply(data_2000_2006, is.numeric)]
desc_stats_2000_2006 <- do.call(rbind, lapply(numeric_vars, function(x) desc_stats(data_2000_2006, x)))
if (!is.null(desc_stats_2000_2006)) {
  print(desc_stats_2000_2006)
}

# Frequency tables for key categorical variables
cat("\n--- FREQUENCY TABLES FOR KEY VARIABLES ---\n")
categorical_vars <- c("region", "female", "age18", "cig", "ccannabis", "cdrink", "wrace")
for (var in categorical_vars) {
  if (var %in% names(data_2000_2006)) {
    cat("\n", toupper(var), ":\n")
    freq_result <- freq_table(data_2000_2006, var)
    if (!is.null(freq_result)) {
      print(freq_result)
    }
  }
}

# Substance use prevalence
cat("\n--- SUBSTANCE USE PREVALENCE (2000-2006) ---\n")
prevalence_2000_2006 <- substance_prevalence(data_2000_2006)
print(prevalence_2000_2006)

# Cross-tabulation of substance use
cat("\n--- SUBSTANCE USE CROSS-TABULATIONS ---\n")
if (all(c("cig", "ccannabis", "cdrink") %in% names(data_2000_2006))) {
  cat("Cigarette × Cannabis use:\n")
  print(table(data_2000_2006$cig, data_2000_2006$ccannabis, useNA = "ifany"))
  
  cat("\nCigarette × Alcohol use:\n")
  print(table(data_2000_2006$cig, data_2000_2006$cdrink, useNA = "ifany"))
  
  cat("\nCannabis × Alcohol use:\n")
  print(table(data_2000_2006$ccannabis, data_2000_2006$cdrink, useNA = "ifany"))
}

# Demographic breakdowns
cat("\n--- DEMOGRAPHIC BREAKDOWNS ---\n")
if (all(c("cig", "female") %in% names(data_2000_2006))) {
  cat("Cigarette use by gender:\n")
  print(table(data_2000_2006$female, data_2000_2006$cig, useNA = "ifany"))
}

if (all(c("cig", "region") %in% names(data_2000_2006))) {
  cat("\nCigarette use by region:\n")
  print(table(data_2000_2006$region, data_2000_2006$cig, useNA = "ifany"))
}

# =================================================================
# DATASET 2: 2017-2023 ANALYSIS
# =================================================================

cat("=================================================================\n")
cat("DATASET 2: 8TH AND 10TH GRADE VAPING DATA 2017-2023\n")
cat("=================================================================\n\n")

# Dataset Structure
cat("--- DATASET STRUCTURE ---\n")
cat("Dimensions:", nrow(data_2017_2023), "rows ×", ncol(data_2017_2023), "columns\n")
cat("Variable names:", paste(names(data_2017_2023), collapse = ", "), "\n")
cat("Variable types:\n")
str(data_2017_2023)

# Years and sample sizes (if year variable exists)
cat("\n--- SAMPLE SIZE INFORMATION ---\n")
if ("year" %in% names(data_2017_2023)) {
  year_table_2017 <- table(data_2017_2023$year)
  print(year_table_2017)
  cat("Year range:", min(data_2017_2023$year, na.rm = TRUE), "-", max(data_2017_2023$year, na.rm = TRUE), "\n")
} else {
  cat("Total sample size:", nrow(data_2017_2023), "\n")
  cat("Note: Year variable not available in 2017-2023 dataset\n")
}

# Missing data analysis
cat("\n--- MISSING DATA ANALYSIS ---\n")
missing_2017_2023 <- missing_summary(data_2017_2023)
print(missing_2017_2023)

# Descriptive statistics for numeric variables
cat("\n--- DESCRIPTIVE STATISTICS (NUMERIC VARIABLES) ---\n")
numeric_vars_2017 <- names(data_2017_2023)[sapply(data_2017_2023, is.numeric)]
desc_stats_2017_2023 <- do.call(rbind, lapply(numeric_vars_2017, function(x) desc_stats(data_2017_2023, x)))
if (!is.null(desc_stats_2017_2023)) {
  print(desc_stats_2017_2023)
}

# Frequency tables for key categorical variables
cat("\n--- FREQUENCY TABLES FOR KEY VARIABLES ---\n")
for (var in categorical_vars) {
  if (var %in% names(data_2017_2023)) {
    cat("\n", toupper(var), ":\n")
    freq_result <- freq_table(data_2017_2023, var)
    if (!is.null(freq_result)) {
      print(freq_result)
    }
  }
}

# Substance use prevalence
cat("\n--- SUBSTANCE USE PREVALENCE (2017-2023) ---\n")
prevalence_2017_2023 <- substance_prevalence(data_2017_2023)
print(prevalence_2017_2023)

# Cross-tabulation of substance use
cat("\n--- SUBSTANCE USE CROSS-TABULATIONS ---\n")
if (all(c("cig", "ccannabis", "cdrink") %in% names(data_2017_2023))) {
  cat("Cigarette × Cannabis use:\n")
  print(table(data_2017_2023$cig, data_2017_2023$ccannabis, useNA = "ifany"))

  cat("\nCigarette × Alcohol use:\n")
  print(table(data_2017_2023$cig, data_2017_2023$cdrink, useNA = "ifany"))

  cat("\nCannabis × Alcohol use:\n")
  print(table(data_2017_2023$ccannabis, data_2017_2023$cdrink, useNA = "ifany"))
}

# Demographic breakdowns
cat("\n--- DEMOGRAPHIC BREAKDOWNS ---\n")
if (all(c("cig", "female") %in% names(data_2017_2023))) {
  cat("Cigarette use by gender:\n")
  print(table(data_2017_2023$female, data_2017_2023$cig, useNA = "ifany"))
}

if (all(c("cig", "region") %in% names(data_2017_2023))) {
  cat("\nCigarette use by region:\n")
  print(table(data_2017_2023$region, data_2017_2023$cig, useNA = "ifany"))
}

# =================================================================
# COMPARATIVE ANALYSIS: 2000-2006 vs 2017-2023
# =================================================================

cat("=================================================================\n")
cat("COMPARATIVE ANALYSIS: 2000-2006 vs 2017-2023\n")
cat("=================================================================\n\n")

# Dataset comparison
cat("--- DATASET COMPARISON ---\n")
cat("2000-2006 Dataset:\n")
cat("  - Dimensions:", nrow(data_2000_2006), "rows ×", ncol(data_2000_2006), "columns\n")
cat("  - Variables:", paste(names(data_2000_2006), collapse = ", "), "\n")

cat("\n2017-2023 Dataset:\n")
cat("  - Dimensions:", nrow(data_2017_2023), "rows ×", ncol(data_2017_2023), "columns\n")
cat("  - Variables:", paste(names(data_2017_2023), collapse = ", "), "\n")

# Variable availability comparison
common_vars <- intersect(names(data_2000_2006), names(data_2017_2023))
only_2000_2006 <- setdiff(names(data_2000_2006), names(data_2017_2023))
only_2017_2023 <- setdiff(names(data_2017_2023), names(data_2000_2006))

cat("\n--- VARIABLE AVAILABILITY ---\n")
cat("Common variables (", length(common_vars), "):", paste(common_vars, collapse = ", "), "\n")
cat("Only in 2000-2006 (", length(only_2000_2006), "):", paste(only_2000_2006, collapse = ", "), "\n")
cat("Only in 2017-2023 (", length(only_2017_2023), "):", paste(only_2017_2023, collapse = ", "), "\n")

# Substance use prevalence comparison
cat("\n--- SUBSTANCE USE PREVALENCE COMPARISON ---\n")
if (nrow(prevalence_2000_2006) > 0 && nrow(prevalence_2017_2023) > 0) {
  comparison_table <- merge(prevalence_2000_2006, prevalence_2017_2023,
                           by = "Substance", suffixes = c("_2000_2006", "_2017_2023"))
  comparison_table$Difference <- comparison_table$Prevalence_Percent_2017_2023 -
                                comparison_table$Prevalence_Percent_2000_2006
  print(comparison_table[, c("Substance", "Prevalence_Percent_2000_2006",
                             "Prevalence_Percent_2017_2023", "Difference")])
}

# Missing data comparison
cat("\n--- MISSING DATA COMPARISON ---\n")
if (nrow(missing_2000_2006) > 0 && nrow(missing_2017_2023) > 0) {
  missing_comparison <- merge(missing_2000_2006, missing_2017_2023,
                             by = "Variable", suffixes = c("_2000_2006", "_2017_2023"))
  missing_comparison$Difference <- missing_comparison$Missing_Percent_2017_2023 -
                                  missing_comparison$Missing_Percent_2000_2006
  print(missing_comparison[, c("Variable", "Missing_Percent_2000_2006",
                              "Missing_Percent_2017_2023", "Difference")])
}

# Data quality assessment
cat("\n--- DATA QUALITY ASSESSMENT ---\n")

# Check for out-of-range values
cat("Range checks for binary variables (should be 0 or 1):\n")
binary_vars <- c("cig", "ccannabis", "cdrink", "female", "age18", "wrace")
for (var in binary_vars) {
  if (var %in% names(data_2000_2006)) {
    unique_vals_2000 <- unique(data_2000_2006[[var]][!is.na(data_2000_2006[[var]])])
    out_of_range_2000 <- unique_vals_2000[!unique_vals_2000 %in% c(0, 1)]
    if (length(out_of_range_2000) > 0) {
      cat("  ", var, "(2000-2006): Out-of-range values:", paste(out_of_range_2000, collapse = ", "), "\n")
    }
  }

  if (var %in% names(data_2017_2023)) {
    unique_vals_2017 <- unique(data_2017_2023[[var]][!is.na(data_2017_2023[[var]])])
    out_of_range_2017 <- unique_vals_2017[!unique_vals_2017 %in% c(0, 1)]
    if (length(out_of_range_2017) > 0) {
      cat("  ", var, "(2017-2023): Out-of-range values:", paste(out_of_range_2017, collapse = ", "), "\n")
    }
  }
}

cat("\n--- ANALYSIS COMPLETE ---\n")
cat("EDA summary generated successfully for both datasets.\n")
cat("Key findings and recommendations should be reviewed for data quality and research implications.\n")
