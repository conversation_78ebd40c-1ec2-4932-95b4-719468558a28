#!/bin/bash

# Variables to search for
variables=("V7648" "V7667" "V7221" "V7226" "V7215" "V7216" "V7102" "V7107" "V7114" "V7231" "V7235" "V7239" "V7240" "V7202" "V1252" "V507" "V1070")

# Years and corresponding codebook files
declare -A codebooks
codebooks["2017"]="/home/<USER>/vaping2_working_data/core_data_0810_codebook/37183-0001-2017-Codebook.txt"
codebooks["2018"]="/home/<USER>/vaping2_working_data/core_data_0810_codebook/37415-0001-2018-Codebook-ICPSR.txt"
codebooks["2019"]="/home/<USER>/vaping2_working_data/core_data_0810_codebook/37842-2019-Codebook-ICPSR.txt"
codebooks["2020"]="/home/<USER>/vaping2_working_data/core_data_0810_codebook/38189-0001-2020-Codebook-ICPSR.txt"
codebooks["2021"]="/home/<USER>/vaping2_working_data/core_data_0810_codebook/38502-0001-2021-Codebook-ICPSR.txt"
codebooks["2022"]="/home/<USER>/vaping2_working_data/core_data_0810_codebook/38883-0001-2022-Codebook-ICPSR.txt"
codebooks["2023"]="/home/<USER>/vaping2_working_data/core_data_0810_codebook/39171-0001-2023-Codebook-ICPSR.txt"

# Output file
output_file="variable_analysis_results.txt"
echo "Variable Analysis Results - 2017-2023" > $output_file
echo "=======================================" >> $output_file
echo "" >> $output_file

# Search for each variable in each year
for year in 2017 2018 2019 2020 2021 2022 2023; do
    echo "=== YEAR $year ===" >> $output_file
    echo "" >> $output_file
    
    codebook_file="${codebooks[$year]}"
    
    if [ -f "$codebook_file" ]; then
        for var in "${variables[@]}"; do
            echo "--- Variable: $var ---" >> $output_file
            
            # Search for the variable and extract relevant information
            grep -n -A 30 -B 5 "^$var:" "$codebook_file" >> $output_file 2>/dev/null
            
            if [ $? -ne 0 ]; then
                echo "Variable $var not found in $year codebook" >> $output_file
            fi
            
            echo "" >> $output_file
        done
    else
        echo "Codebook file not found: $codebook_file" >> $output_file
    fi
    
    echo "" >> $output_file
done

echo "Analysis complete. Results saved to $output_file"
