# Inspect variable labels for 0810 dataset (2000-0001-0810-Data.dta)
# Outputs candidate V-codes for target constructs and shows value labels for key vars

suppressPackageStartupMessages({
  library(haven)
  library(dplyr)
  library(purrr)
})

file <- '/home/<USER>/vaping2_working_data/core_data_0810/2000-0001-0810-Data.dta'
cat('Reading:', file, '\n')
d <- read_dta(file)

get_label <- function(x) as.character(attr(x, 'label'))
var_labels <- sapply(d, get_label)
var_names <- names(d)
lab_df <- tibble(var = var_names, label = unname(var_labels))

show <- function(title, pat, n=40) {
  cat('\n--', title, '--\n')
  hits <- lab_df %>% filter(grepl(pat, label, ignore.case = TRUE) | grepl(pat, var, ignore.case=TRUE))
  print(head(hits, n))
}

# Show core IDs & weights
cat('\nCASEID present:', 'CASEID' %in% names(d), '\n')
cat('V5 present:', 'V5' %in% names(d), '\n')

# Substance use | demographics | behaviors
show('cig (30-day)', 'CIGS SMKD/30DAY|SMOK.*30|CIG.*30')
show('cannabis (30-day)', 'MJ\\+HS/LAST30|MARIJUAN.*30|MJ.*30')
show('alcohol (30-day)', 'ALC.*/30|30D SIPS|30 D[ -]?SIPS|ALCOHOL.*30|30-DAY')
show('region', 'SCHOOL REGION|REGION')
show('sex', "R'S SEX|^SEX$")
show('race', "R'S RACE|RACE")
show('father educ', 'FATH(ER)? .*EDUC|FATHER.*EDUC|FATHR EDUC')
show('mother educ', 'MOTH(ER)? .*EDUC|MOTHER.*EDUC|MOTHR EDUC')
show('skip class', 'SKP CLASS|SKIP CLASS|SKIP .*CLASS')
show('gpa', 'R HS GRADE|GRADE/D=1|HS GRADE')
show('go out', 'GO OUT')
show('work hrs school year', 'HRS/W WRK SCHYR|WRK SCHYR|HRS/W.*WORK .*SCH')
show('college expectations', 'WL DO 4YR CLG|4YR CLG|4[- ]?YEAR COL')
show('dating frequency', 'DATE 3[+]/WK|DATE 3.*WK|DATE 3')
show('age', '^AGE$|R\'S AGE|AGE .*YEARS?')

# For a few likely variables, print value labels
show_vals <- function(varname){
  if (!varname %in% names(d)) return(invisible())
  x <- d[[varname]]
  cat('\nValue labels for', varname, '(', get_label(x), '):\n')
  labs <- attr(x, 'labels')
  if (!is.null(labs)) print(labs) else cat('(no value labels)\n')
}

# Candidates observed previously; will print if present
cands <- c('V1178','V1191','V1181','V507','V1233','V1234','V1252','V1271','V1258','V1130')
invisible(lapply(cands, show_vals))

