# analysis_on_0810data.R
# Coarsened Exact Matching (CEM) analysis comparing cigarette smoking before (2000-2006)
# and after (2017-2023) ENDS availability using MTF 8th/10th grade datasets.
#
# Requirements implemented per spec:
# - Load both period datasets (Stata .dta)
# - Create period indicator (0=pre-ENDS 2000-2006, 1=post-ENDS 2017-2023)
# - DO NOT use *_bin variables from 2017-2023; for 2000-2006 create equivalent binaries
# - Focal outcome: cig (30-day cigarette use)
# - Focal predictor: cvape (ENDS; derive never/ever/current if possible)
# - 11 common liability variables: protective (gpa, collexp, pared), risk (skip, goout, dates, cdrink, ccannabis, work21), demographics (female, age18)
# - Handle missing data: set goout, work21, age18 = NA in pre; implement multiple imputation; document samples
# - CEM matching: pre matched to post subgroups (cvape never/ever/current)
# - Survey-weighted logistic regression on matched data with uncoarsened covariates
# - Outputs: summaries, balance tables, regression ORs, diagnostics; document limitations

suppressPackageStartupMessages({
  library(haven)
  library(dplyr)
  library(ggplot2)
  library(cem)
  library(survey)
  library(mice)
  library(broom)
})

# ---------------------------
# Config
# ---------------------------
DATA_DIR <- "/home/<USER>/vaping2"
PRE_FILE <- file.path(DATA_DIR, "vaping_8th_10th_grade_2000_2006.dta")
POST_FILE <- file.path(DATA_DIR, "vaping_8th_10th_grade_2017_2023.dta")
OUT_DIR <- file.path(DATA_DIR, "analysis_outputs")
if (!dir.exists(OUT_DIR)) dir.create(OUT_DIR, recursive = TRUE)

# ---------------------------
# Helpers
# ---------------------------
log_msg <- function(...) cat(sprintf(paste0("[", format(Sys.time(), "%H:%M:%S"), "] ", paste0(..., collapse=""), "\n")))

safe_write_csv <- function(df, name){
  path <- file.path(OUT_DIR, name)
  readr::write_csv(df, path)
  log_msg("Wrote ", path)
}

# Try to derive cvape categories from available variables
# Returns list: list(cvape_cat=factor levels c("never","ever","current"), source="...", notes="...")
derive_cvape <- function(post){
  res <- list(cvape_cat = NULL, source = NA_character_, notes = NA_character_)
  # If explicit components exist, prefer them
  cand_ever <- intersect(names(post), c("evape", "ever_vape", "vape_ever", "EVAPE"))
  cand_30d  <- intersect(names(post), c("vape30d", "cvapea", "days_vaped_30d", "days_vape_30", "VAPE30D"))
  if (length(cand_ever) > 0 && length(cand_30d) > 0){
    ev <- post[[cand_ever[1]]]
    d30 <- post[[cand_30d[1]]]
    cv <- factor(ifelse(!is.na(d30) & d30 >= 1, "current",
                        ifelse(!is.na(ev) & ev == 1, "ever", "never")),
                 levels = c("never","ever","current"))
    res$cvape_cat <- cv
    res$source <- paste0("derived from ", cand_ever[1], " and ", cand_30d[1])
    res$notes <- "Assumed coding: ever=1; 30-day current if days>=1"
    return(res)
  }
  # Fallback: use cvape if present (assumed 1=never, 2=ever-not-current, 3=current)
  if ("cvape" %in% names(post)){
    cv <- post$cvape
    cv_cat <- factor(ifelse(cv == 3, "current", ifelse(cv == 2, "ever", ifelse(cv == 1, "never", NA))),
                     levels = c("never","ever","current"))
    res$cvape_cat <- cv_cat
    res$source <- "cvape (assumed 1=never, 2=ever-not-current, 3=current)"
    res$notes <- "If this assumption is incorrect, update derive_cvape() mapping."
    return(res)
  }
  res$notes <- "No vaping variables found; cvape categories unavailable."
  res
}

# Create binary variables for pre (2000-2006) using same thresholds as 2017-2023
add_pre_binaries <- function(pre){
  pre <- pre %>% mutate(
    gpa_bin = ifelse(!is.na(gpa) & gpa >= 8, 1L, ifelse(is.na(gpa), NA, 0L)),
    collexp_bin = ifelse(!is.na(collexp) & collexp == 4, 1L, ifelse(is.na(collexp), NA, 0L)),
    pared_bin = ifelse(!is.na(pared) & pared >= 5, 1L, ifelse(is.na(pared), NA, 0L)),
    skip_bin = ifelse(is.na(skip), NA, ifelse(skip %in% 2:6, 1L, 0L)),
    # Not available in 0810 extract
    goout = NA_real_,
    dates = dates, # keep original ordinal if present
    dates_bin = ifelse(is.na(dates), NA, ifelse(dates >= 2, 1L, 0L)),
    work21_bin = NA_integer_,
    age18 = NA_integer_
  )
  pre
}

# CEM coarsening cutpoints for ordinals
cem_cutpoints <- function(data){
  list(
    gpa = c(0, 4, 6, 7, 9),            # <=4, 5-6, 7, 8-9
    collexp = c(0, 2, 3, 4),           # 1-2, 3, 4
    pared = c(0, 3, 4, 5, 6),          # 1-3, 4, 5, 6
    skip = c(0, 1, 3, 6),              # 1, 2-3, 4-6
    dates = c(0, 1, 3, 6)              # 1, 2-3, 4-6
  )
}

# Compute standardized mean difference for binary/ordinal predictors (for diagnostics)
std_diff <- function(x, g, w = NULL){
  if (is.null(w)) w <- rep(1, length(x))
  a <- which(g == 1); b <- which(g == 0)
  mx <- function(ix) sum(w[ix] * x[ix], na.rm = TRUE) / sum(w[ix][!is.na(x[ix])])
  vx <- function(ix) {
    m <- mx(ix); sum(w[ix] * (x[ix]-m)^2, na.rm = TRUE) / sum(w[ix][!is.na(x[ix])])
  }
  m1 <- mx(a); m0 <- mx(b); s <- sqrt( (vx(a)+vx(b))/2 )
  (m1 - m0) / ifelse(s == 0, 1, s)
}

make_balance_table <- function(df, treat, confs, weights=NULL){
  tb <- lapply(confs, function(v){
    x <- df[[v]]; g <- df[[treat]]; w <- weights
    cbind(variable=v,
          pre=NA_real_, post=NA_real_)
  })
  bind_rows(tb)
}

# Run one CEM matching scenario
run_cem_scenario <- function(df_all, confounders, cutpoints, treat_name, treat_label, scenario_tag){
  log_msg("Running CEM scenario: ", scenario_tag, " (", treat_label, ")")
  df <- df_all %>% filter(!is.na(.data[[treat_name]]))
  # Pre-match imbalance (L1) on confounders
  pre_imb <- imbalance(group = df[[treat_name]], data = df[, confounders, drop=FALSE])
  # Prepare cutpoints list subset
  cps <- cutpoints
  cps <- cps[names(cps) %in% confounders]
  # Run CEM
  cem_fit <- cem(treatment = treat_name, data = df, drop = setdiff(names(df), confounders), cutpoints = cps)
  df$cem_w <- cem_fit$w
  matched <- df %>% filter(cem_w > 0)
  post_imb <- imbalance(group = matched[[treat_name]], data = matched[, confounders, drop=FALSE], weights = matched$cem_w)

  # Survey design with combined weights
  matched <- matched %>% mutate(weight_cem = ifelse(is.na(V5), 1, V5) * cem_w)
  des <- svydesign(ids = ~1, weights = ~weight_cem, data = matched)

  # Regression (uncoarsened confounders included)
  fml <- as.formula(paste0("cig ~ ", treat_name, " + ", paste(confounders, collapse = " + ")))
  fit <- svyglm(fml, family = quasibinomial(), design = des)
  coefs <- broom::tidy(fit, conf.int = TRUE, exponentiate = TRUE) # ORs

  # Outputs
  # Balance summary (L1)
  bal <- tibble::tibble(
    scenario = scenario_tag,
    treat_label = treat_label,
    pre_L1 = pre_imb$L1, post_L1 = post_imb$L1,
    n_pre = nrow(df), n_post = nrow(matched)
  )
  safe_write_csv(bal, paste0("balance_L1_", scenario_tag, ".csv"))

  # Regression OR table
  or_tbl <- coefs %>% mutate(scenario = scenario_tag, treat_label = treat_label)
  safe_write_csv(or_tbl, paste0("regression_or_", scenario_tag, ".csv"))

  list(bal=bal, or=or_tbl, match=cem_fit, fit=fit, matched=matched)
}

# ---------------------------
# Main
# ---------------------------
log_msg("Loading datasets...")
pre <- read_dta(PRE_FILE)
post <- read_dta(POST_FILE)

# Period indicator
pre$period <- 0L; post$period <- 1L

# Ensure consistent variable types
# (Convert labelled to numeric for ordinals, keep binaries as numeric 0/1)
conv <- function(x){ if (inherits(x, "haven_labelled")) as.numeric(x) else x }
pre <- as.data.frame(lapply(pre, conv))
post <- as.data.frame(lapply(post, conv))

# Derive pre-period binary variables per thresholds; enforce NA for unavailable measures
pre <- add_pre_binaries(pre)

# Derive cvape categories on post
cvape_info <- derive_cvape(post)
post$cvape_cat <- cvape_info$cvape_cat
log_msg("cvape source: ", cvape_info$source, "; notes: ", cvape_info$notes)

# Combine datasets (keep needed variables)
vars_core <- c("CASEID","V5","year","period","cig","gpa","collexp","pared","skip","dates","cdrink","ccannabis","female","age18","goout","work","cvape","cvape_cat",
               # pre binaries created; for post we will NOT use *_bin in matching; keep for reporting only if present
               "gpa_bin","collexp_bin","pared_bin","skip_bin","dates_bin","work21_bin")
all_names <- unique(c(vars_core, names(pre), names(post)))
keep_pre <- intersect(names(pre), all_names)
keep_post <- intersect(names(post), all_names)
pre_k <- pre[keep_pre]
post_k <- post[keep_post]

# Document sample sizes
safe_write_csv(dplyr::tibble(dataset=c("pre","post"), n=c(nrow(pre_k), nrow(post_k))), "sample_sizes_raw.csv")

# Identify confounders available in both periods for matching
confounders_all <- c("gpa","collexp","pared","skip","dates","cdrink","ccannabis","female","age18","goout","work")
confounders_pre_avail <- confounders_all[confounders_all %in% names(pre_k)]
confounders_post_avail <- confounders_all[confounders_all %in% names(post_k)]
confounders_common <- intersect(confounders_pre_avail, confounders_post_avail)
# Per note: goout, work, age18 are absent in pre 0810; proceed with available variables only
log_msg("Confounders used for matching: ", paste(confounders_common, collapse=", "))

# Multiple imputation on combined data for confounders only (predictors only);
# We avoid imputing outcome (cig) and treatment indicator.
combined <- bind_rows(pre_k, post_k)
imp_vars <- unique(c(confounders_common))
miss_before <- sapply(combined[imp_vars], function(x) sum(is.na(x)))
safe_write_csv(tibble::tibble(variable=imp_vars, missing_n=as.integer(miss_before)), "missing_counts_before_imputation.csv")

# mice setup
m <- 5
meth <- mice::make.method(combined[imp_vars])
# Default methods OK for integers/ordinals (pmm/polyreg/logreg chosen automatically); we leave as is.
pred <- mice::make.predictorMatrix(combined[imp_vars])
# Do not use period or cig as predictors here since we're imputing only confounders subset
imp <- mice::mice(combined[imp_vars], m=m, maxit=10, method=meth, predictorMatrix=pred, print=FALSE)

# Complete datasets (confounders only), then replace in copies of full combined data
completed_list <- lapply(1:m, function(i){
  comp <- mice::complete(imp, action=i)
  df <- combined
  df[imp_vars] <- comp
  df
})

# Document complete-case sizes for comparison
cc_rows <- complete.cases(combined[c(imp_vars, "cig")])
safe_write_csv(dplyr::tibble(n_total=nrow(combined), n_complete_cases=sum(cc_rows)), "complete_case_counts.csv")

# CEM scenarios: define treatment indicator per scenario within each imputed dataset
# a) Pre-ENDS (period==0) vs Post-ENDS cvape never
# b) Pre-ENDS vs Post-ENDS cvape ever
# c) Pre-ENDS vs Post-ENDS cvape current

run_all_scenarios <- function(df){
  # Build scenario datasets
  df_never <- df %>% filter((period==0) | (period==1 & cvape_cat=="never")) %>% mutate(treat = ifelse(period==1, 1L, 0L))
  df_ever  <- df %>% filter((period==0) | (period==1 & cvape_cat %in% c("ever","current"))) %>% mutate(treat = ifelse(period==1, 1L, 0L))
  df_curr  <- df %>% filter((period==0) | (period==1 & cvape_cat=="current")) %>% mutate(treat = ifelse(period==1, 1L, 0L))

  cps <- cem_cutpoints(df)
  res_never <- run_cem_scenario(df_never, confounders_common, cps, "treat", "Post cvape=never vs Pre", "pre_vs_post_never")
  res_ever  <- run_cem_scenario(df_ever,  confounders_common, cps, "treat", "Post cvape=ever vs Pre",  "pre_vs_post_ever")
  res_curr  <- run_cem_scenario(df_curr,  confounders_common, cps, "treat", "Post cvape=current vs Pre","pre_vs_post_current")
  list(never=res_never, ever=res_ever, current=res_curr)
}

# Run over imputations and collect results
all_bal <- list(); all_or <- list()
for (i in seq_len(m)){
  log_msg("Running CEM/regression on imputation ", i, " of ", m)
  res <- run_all_scenarios(completed_list[[i]])
  all_bal[[i]] <- bind_rows(res$never$bal, res$ever$bal, res$current$bal) %>% mutate(imputation=i)
  all_or[[i]]  <- bind_rows(res$never$or,  res$ever$or,  res$current$or) %>% mutate(imputation=i)
}

balances <- bind_rows(all_bal)
results_or <- bind_rows(all_or)

safe_write_csv(balances, "balances_all_imputations.csv")
safe_write_csv(results_or, "regression_or_all_imputations.csv")

# Simple pooling of ORs for the treatment effect term across imputations (Rubin's rules)
pool_term <- function(df, term_name){
  dd <- df %>% filter(term == term_name)
  # Convert OR back to log-odds for pooling
  logOR <- log(dd$estimate)
  SE_logOR <- (log(dd$conf.high) - log(dd$conf.low)) / (2*1.96)
  m <- length(logOR)
  Qbar <- mean(logOR, na.rm=TRUE)
  Ubar <- mean(SE_logOR^2, na.rm=TRUE)
  B <- var(logOR, na.rm=TRUE)
  Tvar <- Ubar + (1 + 1/m) * B
  df <- (m - 1) * (1 + Ubar / ((1+1/m)*B))^2
  OR <- exp(Qbar)
  lower <- exp(Qbar - 1.96*sqrt(Tvar))
  upper <- exp(Qbar + 1.96*sqrt(Tvar))
  tibble::tibble(term=term_name, OR=OR, conf.low=lower, conf.high=upper, m=m, Tvar=Tvar, df=df)
}

pooled_never  <- pool_term(results_or %>% filter(scenario=="pre_vs_post_never"),  term_name="treat")
pooled_ever   <- pool_term(results_or %>% filter(scenario=="pre_vs_post_ever"),   term_name="treat")
pooled_curr   <- pool_term(results_or %>% filter(scenario=="pre_vs_post_current"),term_name="treat")
pooled_all <- bind_rows(
  pooled_never  %>% mutate(scenario="pre_vs_post_never"),
  pooled_ever   %>% mutate(scenario="pre_vs_post_ever"),
  pooled_curr   %>% mutate(scenario="pre_vs_post_current")
)
safe_write_csv(pooled_all, "pooled_OR_treat_across_imputations.csv")

# Sample characteristics by period and cvape category (post only)
sample_chars <- bind_rows(pre_k %>% mutate(period=0), post_k %>% mutate(period=1)) %>%
  mutate(cvape_cat = ifelse(period==0, NA, as.character(post$cvape_cat)[match(CASEID, post$CASEID)])) %>%
  group_by(period, cvape_cat) %>%
  summarize(
    n = n(),
    cig_prev = mean(as.numeric(cig)==1, na.rm=TRUE),
    cdrink_prev = mean(as.numeric(cdrink)==1, na.rm=TRUE),
    ccannabis_prev = mean(as.numeric(ccannabis)==1, na.rm=TRUE),
    .groups = "drop"
  )
safe_write_csv(sample_chars, "sample_characteristics_by_period_cvape.csv")

# Diagnostics: simple plot of L1 pre vs post across scenarios/imputations
try({
  p <- balances %>%
    tidyr::pivot_longer(cols=c(pre_L1, post_L1), names_to="stage", values_to="L1") %>%
    ggplot(aes(x=scenario, y=L1, color=stage)) +
    geom_boxplot() +
    theme_minimal() +
    labs(title="CEM Imbalance (L1) Pre vs Post", x="Scenario", y="L1")
  ggsave(file.path(OUT_DIR, "cem_L1_boxplots.png"), p, width=8, height=4)
  log_msg("Saved ", file.path(OUT_DIR, "cem_L1_boxplots.png"))
}, silent = TRUE)

# Limitations documentation
limitations <- c(
  "0810 (2000-2006) extract lacks goout, work21, and age18; these are set to NA and excluded from matching.",
  "cvape categorization is assumed from 'cvape' if EVAPE/30-day vaping-day variables are not present.",
  "Multiple imputation performed on confounders; survey-weighted models run on matched, imputed datasets and pooled via Rubin's rules for the treatment effect.",
  "CEM coarsening choices for ordinals follow meaningful bins; sensitivity to alternative bins is recommended.")
writeLines(limitations, file.path(OUT_DIR, "analysis_limitations.txt"))
log_msg("Analysis completed. See outputs in ", OUT_DIR)

