#!/usr/bin/env python3

import re
import os

# Define the variables we're looking for
variables_info = {
    'V7648': 'EVER VAPE',
    'V7667': '#DAYS VAPE NIC/30DAY', 
    'V7221': 'HS GRADE/D=1',
    'V7226': 'WL DO 4YR CLG',
    'V7215': 'FATHR EDUC LEVEL',
    'V7216': 'MOTHR EDUC LEVEL',
    'V7102': '#CIGS SMKD/30DAY',
    'V7107': '#X DRNK/LAST30DA',
    'V7114': '#XMJ+HS/LAST30DA',
    'V7231': '#DA/4W SKP CLASS',
    'V7235': 'HRS/WK PAID JOB',
    'V7239': '#X OUT W/O PRNT',
    'V7240': '#X DATE 3+/WK',
    'V7202': "R'S SEX",
    'V1252': 'Respondent Age',
    'V507': 'Region',
    'V1070': 'Race'
}

# Codebook files
codebooks = {
    '2017': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/37183-0001-2017-Codebook.txt',
    '2018': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/37415-0001-2018-Codebook-ICPSR.txt',
    '2019': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/37842-2019-Codebook-ICPSR.txt',
    '2020': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/38189-0001-2020-Codebook-ICPSR.txt',
    '2021': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/38502-0001-2021-Codebook-ICPSR.txt',
    '2022': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/38883-0001-2022-Codebook-ICPSR.txt',
    '2023': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/39171-0001-2023-Codebook-ICPSR.txt'
}

def extract_variable_info(file_path, var_name):
    """Extract variable information from codebook"""
    if not os.path.exists(file_path):
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Look for the variable pattern
        pattern = rf'^{var_name}:\s*(.+?)(?=^\w+:|$)'
        match = re.search(pattern, content, re.MULTILINE | re.DOTALL)
        
        if match:
            var_section = match.group(0)
            
            # Extract key information
            info = {
                'variable_name': var_name,
                'found': True,
                'raw_section': var_section[:1000]  # First 1000 chars
            }
            
            # Extract variable label
            label_match = re.search(rf'{var_name}:\s*(.+?)(?:\n|$)', var_section)
            if label_match:
                info['variable_label'] = label_match.group(1).strip()
            
            # Extract missing values
            missing_match = re.search(r'\(Range of\) Missing Values:\s*(.+?)(?:\n|$)', var_section)
            if missing_match:
                info['missing_values'] = missing_match.group(1).strip()
            
            return info
        else:
            return {'variable_name': var_name, 'found': False}
            
    except Exception as e:
        return {'variable_name': var_name, 'found': False, 'error': str(e)}

def search_by_description(file_path, description_keywords):
    """Search for variables by description keywords"""
    if not os.path.exists(file_path):
        return []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find all variable definitions
        var_pattern = r'^(V\d+):\s*(.+?)(?=^V\d+:|$)'
        matches = re.findall(var_pattern, content, re.MULTILINE | re.DOTALL)
        
        found_vars = []
        for var_name, var_content in matches:
            # Check if any keyword matches
            for keyword in description_keywords:
                if keyword.lower() in var_content.lower():
                    found_vars.append({
                        'variable_name': var_name,
                        'content_snippet': var_content[:200]
                    })
                    break
        
        return found_vars
        
    except Exception as e:
        return []

# Main analysis
print("COMPREHENSIVE VARIABLE ANALYSIS - 2017-2023")
print("=" * 50)

for year in ['2017', '2018', '2019', '2020', '2021', '2022', '2023']:
    print(f"\n=== YEAR {year} ===")
    
    codebook_path = codebooks[year]
    
    for var_name, description in variables_info.items():
        print(f"\n--- {var_name}: {description} ---")
        
        # Try direct search first
        info = extract_variable_info(codebook_path, var_name)
        
        if info and info['found']:
            print(f"✓ Found: {info.get('variable_label', 'No label')}")
            if 'missing_values' in info:
                print(f"  Missing values: {info['missing_values']}")
        else:
            print(f"✗ Not found - searching by keywords...")
            
            # Search by description keywords
            keywords = description.split()
            found_alternatives = search_by_description(codebook_path, keywords)
            
            if found_alternatives:
                print(f"  Possible alternatives found:")
                for alt in found_alternatives[:3]:  # Show top 3
                    print(f"    {alt['variable_name']}: {alt['content_snippet'][:100]}...")
            else:
                print(f"  No alternatives found for keywords: {keywords}")

print("\nAnalysis complete!")
