# Search labels across 0810 datasets for given regex patterns
suppressPackageStartupMessages({
  library(haven)
  library(dplyr)
})

data_dir <- '/home/<USER>/vaping2_working_data/core_data_0810'
years <- 2000:2006

search_patterns <- c(
  AGE = 'AGE|18',
  GOOUT = 'GO OUT|EVENING|EVE|OUT WITH|OUT W/FR|OUT AT NIGHT|NIGHT OUT',
  WORK = 'HRS/W WRK|WRK SCHYR|WORK SCHYR|WORK.*SCHOOL|HOURS.*WORK|HRS/W.*WORK'
)

for (yr in years){
  file <- file.path(data_dir, sprintf('%d-0001-0810-Data.dta', yr))
  d <- read_dta(file)
  labs <- sapply(d, function(x) as.character(attr(x,'label')))
  nms <- names(d)
  df <- data.frame(var=nms, label=unname(labs), stringsAsFactors=FALSE)
  cat('\n==== YEAR', yr, '====\n')
  for (nm in names(search_patterns)){
    pat <- search_patterns[[nm]]
    hits <- df %>% filter(grepl(pat, label, ignore.case=TRUE))
    cat('--', nm, '--\n')
    if (nrow(hits)==0) cat('(none)\n') else print(head(hits, 50))
  }
}

