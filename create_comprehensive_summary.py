#!/usr/bin/env python3

import re
import os

def extract_detailed_variable_info(file_path, var_name):
    """Extract detailed variable information including values and labels"""
    if not os.path.exists(file_path):
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Look for the variable pattern - more flexible
        patterns = [
            rf'^{var_name}:\s*(.+?)(?=^\w+:|$)',  # Original pattern
            rf'^{var_name}\s*(.+?)(?=^\w+:|$)',   # Without colon
        ]
        
        var_section = None
        for pattern in patterns:
            match = re.search(pattern, content, re.MULTILINE | re.DOTALL)
            if match:
                var_section = match.group(0)
                break
        
        if not var_section:
            return None
        
        info = {
            'variable_name': var_name,
            'found': True,
            'variable_label': '',
            'question_text': '',
            'value_labels': {},
            'missing_values': '',
            'frequencies': {}
        }
        
        # Extract variable label (first line after variable name)
        lines = var_section.split('\n')
        for i, line in enumerate(lines):
            if var_name in line and ':' in line:
                info['variable_label'] = line.split(':', 1)[1].strip()
                break
        
        # Extract missing values
        missing_match = re.search(r'\(Range of\) Missing Values:\s*(.+?)(?:\n|$)', var_section)
        if missing_match:
            info['missing_values'] = missing_match.group(1).strip()
        
        # Extract value labels from the format like: 1="None" 2="1-2 days"
        value_label_pattern = r'(\d+)="([^"]+)"'
        value_matches = re.findall(value_label_pattern, var_section)
        for value, label in value_matches:
            info['value_labels'][value] = label
        
        # Extract frequency table
        freq_pattern = r'(\d+|\-\d+)\s+([^:]+):\([^)]+\)\s+(\d+)\s+([\d.]+)\s*%'
        freq_matches = re.findall(freq_pattern, var_section)
        for value, label, freq, pct in freq_matches:
            info['frequencies'][value] = {
                'label': label.strip(),
                'frequency': freq,
                'percentage': pct
            }
        
        # Extract question text (usually between item number and value labels)
        question_pattern = r'Item [Nn]umber[^:]*:\s*\d+[^\n]*\n\n(.*?)(?=\n\s*\d+="|\n\s*Value|$)'
        question_match = re.search(question_pattern, var_section, re.DOTALL)
        if question_match:
            info['question_text'] = question_match.group(1).strip()
        
        return info
        
    except Exception as e:
        return {'variable_name': var_name, 'found': False, 'error': str(e)}

# Variables to analyze
variables_info = {
    'V7648': 'EVER VAPE',
    'V7667': '#DAYS VAPE NIC/30DAY', 
    'V7221': 'HS GRADE/D=1',
    'V7226': 'WL DO 4YR CLG',
    'V7215': 'FATHR EDUC LEVEL',
    'V7216': 'MOTHR EDUC LEVEL',
    'V7102': '#CIGS SMKD/30DAY',
    'V7107': '#X DRNK/LAST30DA',
    'V7114': '#XMJ+HS/LAST30DA',
    'V7231': '#DA/4W SKP CLASS',
    'V7235': 'HRS/WK PAID JOB',
    'V7239': '#X OUT W/O PRNT',
    'V7240': '#X DATE 3+/WK',
    'V7202': "R'S SEX",
    'V1252': 'Respondent Age',
    'V507': 'Region',
    'V1070': 'Race'
}

# Codebook files
codebooks = {
    '2017': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/37183-0001-2017-Codebook.txt',
    '2018': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/37415-0001-2018-Codebook-ICPSR.txt',
    '2019': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/37842-2019-Codebook-ICPSR.txt',
    '2020': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/38189-0001-2020-Codebook-ICPSR.txt',
    '2021': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/38502-0001-2021-Codebook-ICPSR.txt',
    '2022': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/38883-0001-2022-Codebook-ICPSR.txt',
    '2023': '/home/<USER>/vaping2_working_data/core_data_0810_codebook/39171-0001-2023-Codebook-ICPSR.txt'
}

print("COMPREHENSIVE VARIABLE SUMMARY - 2017-2023")
print("=" * 60)

# Create summary for each variable
for var_name, description in variables_info.items():
    print(f"\n{'='*60}")
    print(f"VARIABLE: {var_name} - {description}")
    print(f"{'='*60}")
    
    # Check each year
    for year in ['2017', '2018', '2019', '2020', '2021', '2022', '2023']:
        print(f"\n--- YEAR {year} ---")
        
        info = extract_detailed_variable_info(codebooks[year], var_name)
        
        if info and info['found']:
            print(f"✓ Variable Label: {info['variable_label']}")
            print(f"  Missing Values: {info['missing_values']}")
            
            if info['question_text']:
                print(f"  Question: {info['question_text'][:200]}...")
            
            if info['value_labels']:
                print("  Value Labels:")
                for value, label in sorted(info['value_labels'].items()):
                    print(f"    {value} = \"{label}\"")
            
            if info['frequencies']:
                print("  Frequencies (top 5):")
                sorted_freq = sorted(info['frequencies'].items(), 
                                   key=lambda x: int(x[1]['frequency']) if x[1]['frequency'].isdigit() else 0, 
                                   reverse=True)[:5]
                for value, freq_info in sorted_freq:
                    print(f"    {value}: {freq_info['label']} ({freq_info['frequency']}, {freq_info['percentage']}%)")
        else:
            print("✗ Variable not found")

print(f"\n{'='*60}")
print("ANALYSIS COMPLETE")
print(f"{'='*60}")
