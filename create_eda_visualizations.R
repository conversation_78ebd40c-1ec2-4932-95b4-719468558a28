# Create Key Visualizations for 8th and 10th Grade Vaping Data EDA
# Author: Generated visualization script
# Date: 2025-08-14

library(haven)
library(dplyr)
library(ggplot2)
library(gridExtra)
library(tidyr)

# Load datasets
data_2000_2006 <- read_dta("vaping_8th_10th_grade_2000_2006.dta")
data_2017_2023 <- read_dta("vaping_8th_10th_grade_2017_2023.dta")

# Create comparison dataset for substance use prevalence
prevalence_comparison <- data.frame(
  Period = c("2000-2006", "2000-2006", "2000-2006", "2017-2023", "2017-2023", "2017-2023"),
  Substance = c("Cigarettes", "Cannabis", "Alcohol", "Cigarettes", "Cannabis", "Alcohol"),
  Prevalence = c(27.17, 21.95, 49.19, 4.93, 20.75, 30.00)
)

# 1. Substance Use Prevalence Comparison
p1 <- ggplot(prevalence_comparison, aes(x = Substance, y = Prevalence, fill = Period)) +
  geom_bar(stat = "identity", position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(Prevalence, "%")), 
            position = position_dodge(width = 0.9), vjust = -0.5, size = 3) +
  labs(title = "Substance Use Prevalence: 2000-2006 vs 2017-2023",
       subtitle = "8th and 10th Grade Students",
       x = "Substance", y = "Prevalence (%)",
       fill = "Time Period") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        plot.subtitle = element_text(hjust = 0.5, size = 12)) +
  scale_fill_manual(values = c("2000-2006" = "#E74C3C", "2017-2023" = "#3498DB"))

# 2. Sample sizes by year
# Prepare data for 2000-2006
year_data_2000 <- data_2000_2006 %>%
  count(year) %>%
  mutate(Period = "2000-2006")

# Prepare data for 2017-2023  
year_data_2017 <- data_2017_2023 %>%
  count(year) %>%
  mutate(Period = "2017-2023")

# Combine
year_data_combined <- rbind(year_data_2000, year_data_2017)

p2 <- ggplot(year_data_combined, aes(x = year, y = n, fill = Period)) +
  geom_bar(stat = "identity", alpha = 0.8) +
  geom_text(aes(label = scales::comma(n)), vjust = -0.5, size = 3) +
  labs(title = "Sample Sizes by Year",
       subtitle = "8th and 10th Grade Students",
       x = "Year", y = "Number of Students",
       fill = "Time Period") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        plot.subtitle = element_text(hjust = 0.5, size = 12)) +
  scale_fill_manual(values = c("2000-2006" = "#E74C3C", "2017-2023" = "#3498DB")) +
  scale_y_continuous(labels = scales::comma)

# 3. Missing data comparison
missing_vars <- c("cig", "ccannabis", "cdrink", "female", "age18", "pared", "gpa", "wrace")

missing_2000_2006 <- data_2000_2006 %>%
  summarise_at(missing_vars, ~sum(is.na(.)) / length(.) * 100) %>%
  gather(key = "Variable", value = "Missing_Percent") %>%
  mutate(Period = "2000-2006")

missing_2017_2023 <- data_2017_2023 %>%
  summarise_at(missing_vars, ~sum(is.na(.)) / length(.) * 100) %>%
  gather(key = "Variable", value = "Missing_Percent") %>%
  mutate(Period = "2017-2023")

missing_combined <- rbind(missing_2000_2006, missing_2017_2023)

p3 <- ggplot(missing_combined, aes(x = Variable, y = Missing_Percent, fill = Period)) +
  geom_bar(stat = "identity", position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(Missing_Percent, 1), "%")), 
            position = position_dodge(width = 0.9), vjust = -0.5, size = 2.5) +
  labs(title = "Missing Data Comparison",
       subtitle = "Key Variables Across Time Periods",
       x = "Variable", y = "Missing Data (%)",
       fill = "Time Period") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        plot.subtitle = element_text(hjust = 0.5, size = 12),
        axis.text.x = element_text(angle = 45, hjust = 1)) +
  scale_fill_manual(values = c("2000-2006" = "#E74C3C", "2017-2023" = "#3498DB"))

# 4. Regional distribution comparison
region_2000_2006 <- data_2000_2006 %>%
  count(region) %>%
  mutate(Percent = n / sum(n) * 100,
         Period = "2000-2006",
         Region = case_when(
           region == 1 ~ "Northeast",
           region == 2 ~ "North Central", 
           region == 3 ~ "South",
           region == 4 ~ "West"
         ))

region_2017_2023 <- data_2017_2023 %>%
  count(region) %>%
  mutate(Percent = n / sum(n) * 100,
         Period = "2017-2023",
         Region = case_when(
           region == 1 ~ "Northeast",
           region == 2 ~ "North Central",
           region == 3 ~ "South", 
           region == 4 ~ "West"
         ))

region_combined <- rbind(region_2000_2006, region_2017_2023)

p4 <- ggplot(region_combined, aes(x = Region, y = Percent, fill = Period)) +
  geom_bar(stat = "identity", position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(Percent, 1), "%")), 
            position = position_dodge(width = 0.9), vjust = -0.5, size = 3) +
  labs(title = "Regional Distribution Comparison",
       subtitle = "Geographic Distribution of Students",
       x = "Region", y = "Percentage of Sample",
       fill = "Time Period") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        plot.subtitle = element_text(hjust = 0.5, size = 12)) +
  scale_fill_manual(values = c("2000-2006" = "#E74C3C", "2017-2023" = "#3498DB"))

# Save plots
ggsave("substance_use_prevalence_comparison.png", p1, width = 10, height = 6, dpi = 300)
ggsave("sample_sizes_by_year.png", p2, width = 12, height = 6, dpi = 300)
ggsave("missing_data_comparison.png", p3, width = 10, height = 6, dpi = 300)
ggsave("regional_distribution_comparison.png", p4, width = 10, height = 6, dpi = 300)

# Create a combined plot
combined_plot <- grid.arrange(p1, p2, p3, p4, ncol = 2, nrow = 2)
ggsave("eda_summary_plots.png", combined_plot, width = 16, height = 12, dpi = 300)

cat("Visualizations created successfully!\n")
cat("Files generated:\n")
cat("- substance_use_prevalence_comparison.png\n")
cat("- sample_sizes_by_year.png\n") 
cat("- missing_data_comparison.png\n")
cat("- regional_distribution_comparison.png\n")
cat("- eda_summary_plots.png (combined)\n")
