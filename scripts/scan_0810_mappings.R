# Scan 0810 datasets (2000-2006) for target variable label patterns
suppressPackageStartupMessages({
  library(haven)
  library(dplyr)
  library(purrr)
})

data_dir <- '/home/<USER>/vaping2_working_data/core_data_0810'
years <- 2000:2006

read_labels <- function(file){
  d <- read_dta(file)
  labs <- sapply(d, function(x) as.character(attr(x,'label')))
  tibble(var = names(d), label = unname(labs))
}

patterns <- list(
  cig = 'CIGS SMKD/30DAY|SMOK.*30|CIG.*30',
  ccannabis = 'MJ\\+HS/LAST30|MARIJUAN.*30|MJ.*30',
  cdrink = '#X DRNK/LAST30|DRINK.*30|ALCO.*30|30[- ]?DAY.*DRINK',
  region = 'SCHOOL REGION|REGION',
  age = '^AGE$|R\\'S AGE|RS AGE| AGE ',
  female = 'R\'S SEX|^SEX$',
  wrace = 'R\'S RACE|^RACE$|R S RACE',
  pared_f = 'FATH.*EDUC|FATHR EDUC',
  pared_m = 'MOTH.*EDUC|MOTHR EDUC',
  skip = 'SKP CLASS|SKIP CLASS',
  gpa = 'R HS GRADE|GRADE/D=1|HS GRADE',
  goout = 'GO OUT|AV WK GO OUT|OUT AT NIGHT|NIGHT OUT|OUT W/FR',
  work = 'HRS/W WRK SCHYR|WRK SCHYR|WORK .*SCHOOL|HRS/W.*WORK',
  collexp = 'WL DO 4YR CLG|4YR CLG',
  dates = '#X DATE 3\+/WK|DATE 3.*/WK|DATE 3',
  caseid = '^CASEID$',
  weight = '^V5$'
)

scan_year <- function(yr){
  file <- file.path(data_dir, sprintf('%d-0001-0810-Data.dta', yr))
  df <- read_labels(file)
  out <- lapply(patterns, function(pat){
    df %>% filter(grepl(pat, label, ignore.case=TRUE) | grepl(pat, var, ignore.case=TRUE)) %>% 
      slice_head(n=5)
  })
  out
}

all <- lapply(years, scan_year)
names(all) <- as.character(years)

# Print concise summary: first match per key per year
cat('=== SUMMARY: first match per variable per year ===\n')
for (key in names(patterns)){
  cat('\n--', key, '--\n')
  for (yr in years){
    df <- all[[as.character(yr)]][[key]]
    sel <- if (is.null(df) || nrow(df)==0) 'NA' else paste(df$var[1], df$label[1], sep=': ')
    cat(yr, '=>', sel, '\n')
  }
}

# Save detailed CSV for review
rows <- list()
for (yr in years){
  for (key in names(patterns)){
    df <- all[[as.character(yr)]][[key]]
    if (!is.null(df) && nrow(df)>0){
      df$year <- yr
      df$key <- key
      rows[[length(rows)+1]] <- df
    }
  }
}
if (length(rows)>0){
  det <- bind_rows(rows) %>% select(year, key, var, label)
  readr::write_csv(det, '0810_mapping_candidates.csv')
  cat('\nWrote 0810_mapping_candidates.csv with mapping candidates.\n')
}

