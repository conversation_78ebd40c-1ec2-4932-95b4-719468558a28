suppressPackageStartupMessages({
  library(haven)
  library(dplyr)
})

data_dir <- '/home/<USER>/vaping2_working_data/core_data_0810'
years <- 2000:2006

find_first <- function(df, pat){
  hits <- df %>% filter(grepl(pat, label, ignore.case=TRUE))
  if (nrow(hits)==0) return(NA_character_)
  paste0(hits$var[1], ': ', hits$label[1])
}

scan <- function(yr){
  f <- file.path(data_dir, sprintf('%d-0001-0810-Data.dta', yr))
  d <- read_dta(f)
  labs <- sapply(d, function(x) as.character(attr(x, 'label')))
  df <- data.frame(var=names(d), label=unname(labs), stringsAsFactors=FALSE)
  cat('\n==== YEAR', yr, '====\n')
  # GO OUT / EVENINGS / FUN / RECREATION / NIGHT / SOCIAL
  cat('goout candidate:', find_first(df, 'GO OUT|EVENING|OUT AT NIGHT|NIGHT OUT|FUN|RECREATION|SOCIAL|OUT WITH'), '\n')
  # DATE with month timeframe
  cat('dates (month) candidate:', find_first(df, 'DATE.*LAST|DATE.*30|DATE.*4 ?W'), '\n')
}

invisible(lapply(years, scan))

