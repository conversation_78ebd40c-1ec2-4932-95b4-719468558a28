suppressPackageStartupMessages({library(haven);library(dplyr)})
find_dates <- function(yr){
  f <- sprintf('/home/<USER>/vaping2_working_data/core_data_0810/%d-0001-0810-Data.dta', yr)
  d <- read_dta(f)
  labs <- sapply(d, function(x) as.character(attr(x,'label')))
  nms <- names(d)
  df <- data.frame(var=nms, label=unname(labs), stringsAsFactors=FALSE)
  hits <- df %>% filter(grepl('DATE 3', label, ignore.case=TRUE))
  cat('\n', yr, '\n'); print(hits)
}
invisible(lapply(2000:2006, find_dates))

